import re
import zipfile
import logging
import hashlib
import os
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from itertools import groupby

# 配置日志
logger = logging.getLogger(__name__)



class DocxParser:
    """DocX解析器"""
    
    def __init__(self):
        self.logger = logger
    
    def read_docx_document(self, docx_path: str) -> BeautifulSoup:
        """
        解析DOTX文件
        
        Args:
            dotx_path: DOTX文件路径
            
        Returns:
            Tuple[DotxStyle, Optional[str]]: (解析结果, 错误信息)
        """
        self.logger.info(f"开始解析DOTX文件: {docx_path}")
        
        try:
            with zipfile.ZipFile(docx_path, 'r') as zip_ref:
                template_styles_xml = zip_ref.read('word/document.xml')
                template_soup = BeautifulSoup(template_styles_xml, 'xml')
                self.logger.debug("成功解析styles.xml")
                return template_soup
            
            return None
        except Exception as e:
            error_msg = f"解析DOTX文件时出错: {str(e)}"
            self.logger.error(error_msg)
            return None

    def save_docx_document(self, docx_path: str, xml: BeautifulSoup) -> bool:
        """
        保存修改后的XML内容到DOCX文件

        Args:
            docx_path: DOCX文件路径
            xml: 修改后的BeautifulSoup XML对象

        Returns:
            bool: 保存是否成功
        """
        self.logger.info(f"开始保存DOCX文件: {docx_path}")

        try:
            # 创建临时文件路径
            temp_path = docx_path + ".tmp"

            # 读取原始DOCX文件并创建新的ZIP文件
            with zipfile.ZipFile(docx_path, 'r') as original_zip:
                with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as new_zip:
                    # 复制所有文件，除了word/documents.xml
                    for item in original_zip.infolist():
                        if item.filename != 'word/document.xml':
                            # 复制其他文件
                            data = original_zip.read(item.filename)
                            new_zip.writestr(item, data)

                    # 写入修改后的documents.xml
                    modified_xml = str(xml)
                    new_zip.writestr('word/document.xml', modified_xml)

            # 替换原文件
            if os.path.exists(temp_path):
                if os.path.exists(docx_path):
                    os.remove(docx_path)
                os.rename(temp_path, docx_path)

                self.logger.info(f"成功保存DOCX文件: {docx_path}")
                return True
            else:
                self.logger.error("临时文件创建失败")
                return False

        except Exception as e:
            error_msg = f"保存DOCX文件时出错: {str(e)}"
            self.logger.error(error_msg)

            # 清理临时文件
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass

            return False

    def saveMeetingText2docx(self, title: str, segments: List['TextSegment'], document_xml: BeautifulSoup) -> BeautifulSoup:
        """
        将会议文本按说话人分组保存到DOCX文档XML中

        Args:
            title: 会议标题
            segments: 文本段落列表
            document_xml: DOCX文档的XML对象

        Returns:
            BeautifulSoup: 修改后的XML对象
        """
        self.logger.info(f"开始处理会议文本，标题: {title}, 段落数: {len(segments)}")

        try:
            # 1. 替换标题
            self._replace_title_in_xml(document_xml, title)

            # 2. 获取样式信息
            speaker_style = self._get_speaker_style(document_xml)
            content_style = self._get_content_style(document_xml)

            # 3. 按说话人分组
            grouped_segments = self._group_segments_by_speaker(segments)

            # 4. 清除现有内容（保留标题）
            self._clear_existing_content(document_xml)

            # 5. 添加分组后的内容
            self._add_grouped_content(document_xml, grouped_segments, speaker_style, content_style)

            self.logger.info("会议文本处理完成")
            return document_xml

        except Exception as e:
            error_msg = f"处理会议文本时出错: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def _replace_title_in_xml(self, document_xml: BeautifulSoup, title: str):
        """替换XML中的标题"""
        # 查找包含"标题"的文本节点
        title_elements = document_xml.find_all(string=lambda text: text and "标题" in text)
        for element in title_elements:
            element.replace_with(title)

    def _get_speaker_style(self, document_xml: BeautifulSoup) -> dict:
        """获取说话人的样式"""
        # 查找包含"说话人"的段落，提取其样式
        speaker_elements = document_xml.find_all(string=lambda text: text and "说话人" in text)
        if speaker_elements:
            # 找到父段落元素
            parent_p = speaker_elements[0].find_parent('w:p')
            if parent_p:
                # 提取段落属性
                p_pr = parent_p.find('w:pPr')
                if p_pr:
                    return {'pPr': str(p_pr)}
        return {}

    def _get_content_style(self, document_xml: BeautifulSoup) -> dict:
        """获取文本内容的样式"""
        # 查找包含"文本内容"的段落，提取其样式
        content_elements = document_xml.find_all(string=lambda text: text and "文本内容" in text)
        if content_elements:
            # 找到父段落元素
            parent_p = content_elements[0].find_parent('w:p')
            if parent_p:
                # 提取段落属性
                p_pr = parent_p.find('w:pPr')
                if p_pr:
                    return {'pPr': str(p_pr)}
        return {}

    def _group_segments_by_speaker(self, segments: List['TextSegment']) -> List[dict]:
        """按说话人分组段落"""
        grouped = []

        # 按说话人分组
        segments_sorted = sorted(segments, key=lambda x: (x.speaker, x.start_time))

        for speaker, group in groupby(segments_sorted, key=lambda x: x.speaker):
            group_list = list(group)
            if group_list:
                start_time = min(seg.start_time for seg in group_list)
                end_time = max(seg.end_time for seg in group_list)
                texts = [seg.text for seg in group_list if seg.text.strip()]

                grouped.append({
                    'speaker': speaker,
                    'start_time': start_time,
                    'end_time': end_time,
                    'texts': texts
                })

        return grouped

    def _clear_existing_content(self, document_xml: BeautifulSoup):
        """清除现有内容，保留标题"""
        # 找到文档主体
        body = document_xml.find('w:body')
        if body:
            # 保留第一个段落（通常是标题）
            paragraphs = body.find_all('w:p')
            if len(paragraphs) > 1:
                for p in paragraphs[1:]:
                    p.decompose()

    def _add_grouped_content(self, document_xml: BeautifulSoup, grouped_segments: List[dict],
                           speaker_style: dict, content_style: dict):
        """添加分组后的内容到XML"""
        body = document_xml.find('w:body')
        if not body:
            return

        for group in grouped_segments:
            # 添加说话人标题段落
            speaker_header = f"{group['speaker']} {group['start_time']:.1f}s-{group['end_time']:.1f}s"
            self._add_paragraph(body, speaker_header, speaker_style)

            # 添加文本内容段落
            for text in group['texts']:
                if text.strip():
                    self._add_paragraph(body, f"  {text}", content_style)

    def _add_paragraph(self, body, text: str, style: dict):
        """添加段落到文档"""
        # 获取文档根对象来创建新标签
        soup = body.find_parent() or body
        while soup.parent:
            soup = soup.parent

        # 创建新段落
        new_p = soup.new_tag('w:p')

        # 添加段落属性（如果有样式）
        if style.get('pPr'):
            p_pr_soup = BeautifulSoup(style['pPr'], 'xml')
            p_pr = p_pr_soup.find('w:pPr')
            if p_pr:
                new_p.append(p_pr)

        # 创建文本运行
        r = soup.new_tag('w:r')
        t = soup.new_tag('w:t')
        t.string = text
        r.append(t)
        new_p.append(r)

        # 添加到文档
        body.append(new_p)


@dataclass
class TextSegment:
    """文本段落数据类"""
    start_time: float
    end_time: float
    speaker: str
    text: str
