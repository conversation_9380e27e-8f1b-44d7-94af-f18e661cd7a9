import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Literal, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.models import Meeting, MeetingTextRefineTask, HttpResult, success, error, TaskStatus
import logging
from app.mysql import get_db
from app.utils import copy_file_to_oss, upload_text_to_oss
from config import cfg
from app.models import Meeting
from app.tasks.utils import startRefineTask

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/eport", tags=["导出接口"])


class ExportMeetingTextRequest(BaseModel):
    id: int
    isRefined: bool 


@router.post("/text", response_model=HttpResult[bool], summary="导出会议文本")
async def task_callback(
    req: ExportMeetingTextRequest,
    db: Session = Depends(get_db)
):

    #会议文本使用Meeting.textUrl(json文件) 中的speaker_segments[] 
    #微调后的文本使用
